import  { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, TileLayer, <PERSON>ygon, useMap<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>up, <PERSON> } from 'react-leaflet';
import L from 'leaflet';
import { Unit, Equipment, AITargetingResult, BattleSimulationResult } from '../types';
import { getInitialUnits, getInitialEquipment, getAITargetRecommendations, getInitialAnalyses } from '../data/mockData';
import MapControls from './MapControls';
import SearchBar from './SearchBar';
import BattleAnimation from './BattleAnimation';
import MapGallery from './MapGallery';
import { Target, X, ArrowDown, ZapIcon, Move, Layers, Mountain, Eye, Swords, FileText, Zap } from 'lucide-react';
import CircleRangeConfig from './CircleRangeConfig';
import TerrainAnalysisLayer from './TerrainAnalysisLayer';
import SpatialAnalysisControls from './SpatialAnalysisControls';
import RouteAnalysisLayer from './RouteAnalysisLayer';
import BattleSimulator from './BattleSimulator';
import BattleSimulationEffects from './BattleSimulationEffects';
import BattleForceSelector from './BattleForceSelector';
import ScenarioPlanner from './ScenarioPlanner';
import * as turf from '@turf/turf';
import RealBattleSimulation from './RealBattleSimulation';
import AdvancedBattleSimulation from './AdvancedBattleSimulation';
import MapBattleSimulation from './MapBattleSimulation';
import BattleResultFlash from './BattleResultFlash';

// Define unit icon component to display on map
const UnitIcon = ({ 
  type, 
  side, 
  isSelected = false,
  isTarget = false 
}: { 
  type: string; 
  side: 'friendly' | 'enemy';
  isSelected?: boolean;
  isTarget?: boolean;
}) => {
  // تحديد اللون الأساسي بناءً على الجانب
  let color = side === 'friendly' ? '#3b82f6' : '#ef4444';
  
  // تغيير اللون إذا كانت الوحدة مختارة أو مستهدفة
  if (isSelected) {
    color = '#10b981'; // أخضر للوحدات المختارة
  } else if (isTarget) {
    color = '#f59e0b'; // برتقالي للوحدات المستهدفة
  }
  
  // تحديد حجم الأيقونة (أكبر للوحدات المختارة)
  const size = isSelected || isTarget ? '30px' : '24px';
  
  // تحديد سمك الحدود (أكبر للوحدات المختارة)
  const borderWidth = isSelected || isTarget ? '3px' : '2px';
  
  const baseStyle = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '50%',
    backgroundColor: color,
    color: 'white',
    border: `${borderWidth} solid white`,
    boxShadow: isSelected || isTarget ? '0 0 8px rgba(255,255,255,0.8), 0 0 12px ' + color : '0 2px 4px rgba(0,0,0,0.3)',
    fontSize: '12px',
    fontWeight: 'bold',
    width: size,
    height: size,
    zIndex: isSelected || isTarget ? 1000 : 500
  };
  
  // Different sizes or styles could be applied based on unit type
  let style = { ...baseStyle };
  let text = '';
  
  switch (type) {
    case 'division':
      style = { ...style, width: '28px', height: '28px' };
      text = 'فق';
      break;
    case 'brigade':
      text = 'لو';
      break;
    case 'battalion':
      text = 'كت';
      break;
    case 'company':
      style = { ...style, width: '20px', height: '20px' };
      text = 'سر';
      break;
    default:
      text = 'وح';
  }
  
  return (
    <div style={style}>
      {text}
    </div>
  );
};

// Equipment icon component
const EquipmentIcon = ({ type }: { type: string }) => {
  const baseStyle = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '50%',
    backgroundColor: '#10b981',
    color: 'white',
    border: '2px solid white',
    boxShadow: '0 2px 4px rgba(0,0,0,0.3)',
    width: '24px',
    height: '24px'
  };
  
  return (
    <div style={baseStyle}>
      {type.charAt(0).toUpperCase()}
    </div>
  );
};

// Custom map controller to handle events
function MapController({ onMapClick }: { onMapClick: (latlng: L.LatLng) => void }) {
  useMapEvents({
    click(e) {
      onMapClick(e.latlng);
    },
  });
  return null;
}

// Main MapView component
export default function MapView() {
  const [unitsData, setUnitsData] = useState<Unit[]>([]);
  const [equipmentData, setEquipmentData] = useState<Equipment[]>([]);
  const [selectedUnitId, setSelectedUnitId] = useState<string | null>(null);
  const [targetUnitId, setTargetUnitId] = useState<string | null>(null);
  const [targetingMode, setTargetingMode] = useState(false);
  const [moveMode, setMoveMode] = useState(false);
  const [aiRecommendation, setAiRecommendation] = useState<AITargetingResult | null>(null);
  const [showTargetingUI, setShowTargetingUI] = useState(false);
  const [showBattleScenarioUI, setShowBattleScenarioUI] = useState(false);
  const [animationConfig, setAnimationConfig] = useState<{
    source: [number, number];
    target: [number, number];
    isPlaying: boolean;
  } | null>(null);
  const [mapLayers, setMapLayers] = useState({
    friendly: true,
    enemy: true,
    equipment: true,
    terrain: false,
    obstacles: false,
  });
  const [zoomLevel, setZoomLevel] = useState(8);
  const [mapCenter, setMapCenter] = useState<[number, number]>([35, 38]); // Syria center
  const mapRef = useRef<L.Map | null>(null);
  const [satelliteView, setSatelliteView] = useState(false);
  const [mapImages] = useState([
    "https://images.unsplash.com/photo-1577086664693-894d8405334a?ixid=M3w3MjUzNDh8MHwxfHNlYXJjaHwxfHxzYXRlbGxpdGUlMjB2aWV3JTIwc3lyaWElMjBtYXAlMjBtaWxpdGFyeXxlbnwwfHx8fDE3NDg4NDk5MTl8MA&ixlib=rb-4.1.0&fit=fillmax&h=600&w=800",
    "https://images.unsplash.com/photo-1524661135-423995f22d0b?ixid=M3w3MjUzNDh8MHwxfHNlYXJjaHwyfHxzYXRlbGxpdGUlMjB2aWV3JTIwc3lyaWElMjBtYXAlMjBtaWxpdGFyeXxlbnwwfHx8fDE3NDg4NDk5MTl8MA&ixlib=rb-4.1.0&fit=fillmax&h=600&w=800"
  ]);
  const [showMapGallery, setShowMapGallery] = useState(false);

  // Advanced Battle Simulation states
  const [battleSelectionMode, setBattleSelectionMode] = useState(false);
  const [selectedFriendlyUnits, setSelectedFriendlyUnits] = useState<string[]>([]);
  const [selectedEnemyUnits, setSelectedEnemyUnits] = useState<string[]>([]);
  const [showAdvancedBattleSimulation, setShowAdvancedBattleSimulation] = useState(false);
  const [currentTargetingData, setCurrentTargetingData] = useState<{
    targetUnitId: string;
    attackerUnitId: string;
    weaponType: string;
    accuracy: number;
  } | null>(null);

  // Map Battle Simulation states
  const [mapBattleActive, setMapBattleActive] = useState(false);
  const [battleResult, setBattleResult] = useState<{
    winner: 'friendly' | 'enemy' | 'draw';
    friendlyLosses: number;
    enemyLosses: number;
    duration: number;
    summary: string;
  } | null>(null);
  const [moveSourceUnitId, setMoveSourceUnitId] = useState<string | null>(null);
  const [moveTargetPosition, setMoveTargetPosition] = useState<[number, number] | null>(null);
  const [battleScenario, setBattleScenario] = useState<{
    type: 'attack' | 'defend' | 'reinforce' | 'withdraw';
    attackerUnitIds: string[];
    defenderUnitIds: string[];
    targetPosition?: [number, number];
    probability?: number;
    estimatedLosses?: number;
    equipmentPositions?: { equipmentId: string; newPosition: [number, number] }[];
  } | null>(null);
  const [showSpatialAnalysisUI, setShowSpatialAnalysisUI] = useState(false);
  const [spatialAnalysisDistance, setSpatialAnalysisDistance] = useState(10);
  const [targetingDistance, setTargetingDistance] = useState(30);
  const [targetingReadiness, setTargetingReadiness] = useState(60);
  const [targetingWeaponType, setTargetingWeaponType] = useState<string>('all');
  const [showCircleAnalysisSingle, setShowCircleAnalysisSingle] = useState(false);
  const [showCircleAnalysisAll, setShowCircleAnalysisAll] = useState(false);
  const [showCircleAnalysisFlash, setShowCircleAnalysisFlash] = useState(false);
  const [showThreatenedUnitsFlash, setShowThreatenedUnitsFlash] = useState(false);
  const [showCircleAnalysisSingleWithTerrain, setShowCircleAnalysisSingleWithTerrain] = useState(false);
  const [showCircleAnalysisSingleWithObstacles, setShowCircleAnalysisSingleWithObstacles] = useState(false);
  const [showCircleAnalysisMultiple, setShowCircleAnalysisMultiple] = useState(false);
  const [multipleSelectedUnits, setMultipleSelectedUnits] = useState<string[]>([]);
  const [showCircleConfig, setShowCircleConfig] = useState(false);
  const [circleConfig, setCircleConfig] = useState(() => {
    const saved = localStorage.getItem('circle_range_config');
    return saved ? JSON.parse(saved) : null;
  });
  
  // Estados para análisis espacial avanzado
  const [showTerrainAnalysis, setShowTerrainAnalysis] = useState(false);
  const [showCoverageAnalysis, setShowCoverageAnalysis] = useState(false);
  const [showLOSAnalysis, setShowLOSAnalysis] = useState(false);
  const [showRouteAnalysis, setShowRouteAnalysis] = useState(false);
  const [routeStartUnitId, setRouteStartUnitId] = useState<string | null>(null);
  const [routeEndUnitId, setRouteEndUnitId] = useState<string | null>(null);
  
  // Estados para simulación de combate y planificación de escenarios
  const [showBattleSimulator, setShowBattleSimulator] = useState(false);
  const [showBattleForceSelector, setShowBattleForceSelector] = useState(false);
  const [battleSimulationResult, setBattleSimulationResult] = useState<BattleSimulationResult | null>(null);
  const [showBattleSimulationEffects, setShowBattleSimulationEffects] = useState(false);
  const [autoSimulationActive, setAutoSimulationActive] = useState(false);
  const [selectedBattleForces, setSelectedBattleForces] = useState<{
    friendlyUnits: string[];
    enemyUnits: string[];
    primaryWeapons: string[];
  } | null>(null);
  const [showScenarioPlanner, setShowScenarioPlanner] = useState(false);
  const [activeScenario, setActiveScenario] = useState<any | null>(null);
  const [showEditCirclesSingleWithObstacles, setShowEditCirclesSingleWithObstacles] = useState(false);
  const [showEditCirclesAllWithObstacles, setShowEditCirclesAllWithObstacles] = useState(false);
  const [showRealBattleSimulation, setShowRealBattleSimulation] = useState(false);
  
  // Load units and equipment on component mount
  useEffect(() => {
    // Clear localStorage to ensure we get fresh dispersed positions
    // Remove only unit and equipment position entries
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('unit_position_') || key.startsWith('equipment_position_')) {
        localStorage.removeItem(key);
      }
    });
    
    // Load initial data with dispersed positions
    const initialUnits = getInitialUnits();
    const initialEquipment = getInitialEquipment(initialUnits);
    setUnitsData(initialUnits);
    setEquipmentData(initialEquipment);
    
    // Save the new positions to localStorage for persistence
    initialUnits.forEach(unit => {
      localStorage.setItem(`unit_position_${unit.id}`, JSON.stringify(unit));
    });
    
    initialEquipment.forEach(eq => {
      localStorage.setItem(`equipment_position_${eq.id}`, JSON.stringify(eq));
    });
  }, []);

  // إضافة مرجع للخريطة في النافذة
  useEffect(() => {
    if (mapRef.current) {
      (window as any).map = mapRef.current;
    }
  }, [mapRef.current]);

  // البيانات الافتراضية للتضاريس - محاكاة ارتفاعات واقعية للمنطقة السورية
  const terrainData = [
    // جبال القلمون
    {
      id: 'mountain_1',
      name: 'جبال القلمون',
      type: 'mountain',
      elevation: 2800,
      coordinates: [[34.2, 36.6], [34.4, 36.8], [34.6, 36.7], [34.5, 36.5], [34.2, 36.6]],
      color: '#8B4513'
    },
    // التلال الساحلية
    {
      id: 'hills_1',
      name: 'التلال الساحلية',
      type: 'hills',
      elevation: 800,
      coordinates: [[35.8, 35.9], [36.1, 36.2], [36.2, 36.0], [35.9, 35.7], [35.8, 35.9]],
      color: '#CD853F'
    },
    // هضبة الجولان
    {
      id: 'plateau_1',
      name: 'هضبة الجولان',
      type: 'plateau',
      elevation: 1200,
      coordinates: [[33.1, 35.8], [33.3, 36.0], [33.4, 35.9], [33.2, 35.7], [33.1, 35.8]],
      color: '#A0522D'
    },
    // جبال الأنصارية
    {
      id: 'mountain_2',
      name: 'جبال الأنصارية',
      type: 'mountain',
      elevation: 1562,
      coordinates: [[35.2, 36.1], [35.5, 36.3], [35.6, 36.1], [35.3, 35.9], [35.2, 36.1]],
      color: '#8B4513'
    },
    // منطقة تضاريس كبيرة للاختبار - تغطي منطقة واسعة
    {
      id: 'test_terrain_1',
      name: 'منطقة جبلية للاختبار',
      type: 'mountain',
      elevation: 1800,
      coordinates: [[35.4, 36.0], [35.7, 36.0], [35.7, 36.4], [35.4, 36.4], [35.4, 36.0]],
      color: '#654321'
    },
    // منطقة تضاريس أخرى للاختبار
    {
      id: 'test_terrain_2',
      name: 'هضبة للاختبار',
      type: 'plateau',
      elevation: 1000,
      coordinates: [[35.3, 36.1], [35.6, 36.1], [35.6, 36.3], [35.3, 36.3], [35.3, 36.1]],
      color: '#8B7355'
    }
  ];

  // البيانات الافتراضية للعوائق - سواتر ترابية وحواجز دفاعية
  const obstaclesData = [
    {
      id: 'barrier_1',
      name: 'ساتر ترابي شمالي',
      type: 'earthwork',
      coordinates: [[34.8, 36.9], [34.9, 37.1], [34.95, 37.05], [34.85, 36.85]],
      height: 3,
      color: '#8B4513'
    },
    {
      id: 'barrier_2',
      name: 'خندق دفاعي',
      type: 'trench',
      coordinates: [[35.1, 36.2], [35.3, 36.4], [35.35, 36.35], [35.15, 36.15]],
      depth: 2,
      color: '#654321'
    },
    {
      id: 'barrier_3',
      name: 'حاجز خرساني',
      type: 'concrete_barrier',
      coordinates: [[34.5, 37.2], [34.7, 37.4], [34.75, 37.35], [34.55, 37.15]],
      height: 2.5,
      color: '#696969'
    },
    {
      id: 'barrier_4',
      name: 'أسلاك شائكة',
      type: 'wire_fence',
      coordinates: [[35.4, 36.8], [35.6, 37.0], [35.65, 36.95], [35.45, 36.75]],
      height: 1.5,
      color: '#2F4F4F'
    },
    {
      id: 'barrier_5',
      name: 'ساتر رملي جنوبي',
      type: 'sand_barrier',
      coordinates: [[33.8, 36.3], [34.0, 36.5], [34.05, 36.45], [33.85, 36.25]],
      height: 2.8,
      color: '#F4A460'
    },
    // عوائق كبيرة للاختبار
    {
      id: 'test_obstacle_1',
      name: 'منطقة عوائق للاختبار',
      type: 'concrete_barrier',
      coordinates: [[34.2, 36.2], [34.4, 36.2], [34.4, 36.4], [34.2, 36.4], [34.2, 36.2]],
      height: 4,
      color: '#696969'
    },
    {
      id: 'test_obstacle_2',
      name: 'حاجز كبير للاختبار',
      type: 'earthwork',
      coordinates: [[35.2, 37.2], [35.4, 37.2], [35.4, 37.4], [35.2, 37.4], [35.2, 37.2]],
      height: 5,
      color: '#8B4513'
    }
  ];
  
  // Get unit by ID helper
  const getUnitById = (id: string): Unit | undefined => {
    return unitsData.find((unit: Unit) => unit.id === id);
  };
  
  // Get stable position for equipment (avoid random repositioning)
  const getStableEquipmentPosition = (eq: Equipment, unit: Unit): [number, number] => {
    if (unit.location.type !== 'polygon') return [0, 0];
    
    const coords = unit.location.coordinates as number[][][];
    const centerLat = coords[0].reduce((sum, point) => sum + point[0], 0) / coords[0].length;
    const centerLng = coords[0].reduce((sum, point) => sum + point[1], 0) / coords[0].length;
    
    // Use equipment ID to create a stable offset
    const idSum = eq.id.split('').reduce((sum, char) => sum + char.charCodeAt(0), 0);
    const offsetScale = 0.01;
    const offsetX = ((idSum % 100) / 100) * offsetScale * 2 - offsetScale;
    const offsetY = ((idSum % 50) / 50) * offsetScale * 2 - offsetScale;
    
    return [centerLat + offsetX, centerLng + offsetY];
  };
  
  // دالة للعثور على القوات الصديقة التي يمكنها الوصول إلى الهدف
  const getFriendlyUnitsInRange = (targetUnit: Unit) => {
    const [targetLat, targetLng] = getUnitCenter(targetUnit);

    return unitsData.filter(unit => {
      if (unit.side !== 'friendly') return false;

      const [unitLat, unitLng] = getUnitCenter(unit);
      const { blind, kill, nonkill } = getCircleRanges(unit);

      // حساب المسافة بين الوحدة والهدف
      const distance = getDistanceKm(unitLat, unitLng, targetLat, targetLng);

      // التحقق من وجود الهدف ضمن أي من الدوائر
      return distance <= Math.max(blind, kill, nonkill);
    });
  };

  // Handle unit click on map
  const handleUnitClick = (unitId: string) => {
    const clickedUnit = getUnitById(unitId);
    if (!clickedUnit) return;

    if (battleSelectionMode) {
      // وضع اختيار الوحدات للمعركة
      if (clickedUnit.side === 'friendly') {
        setSelectedFriendlyUnits(prev =>
          prev.includes(unitId)
            ? prev.filter(id => id !== unitId)
            : [...prev, unitId]
        );
      } else if (clickedUnit.side === 'enemy') {
        setSelectedEnemyUnits(prev =>
          prev.includes(unitId)
            ? prev.filter(id => id !== unitId)
            : [...prev, unitId]
        );
      }
      return;
    }

    if (targetingMode) {
      if (clickedUnit.side === 'enemy') {
        setTargetUnitId(unitId);

        // العثور على القوات الصديقة التي يمكنها الوصول إلى الهدف
        const friendlyUnitsInRange = getFriendlyUnitsInRange(clickedUnit);
        console.log('🎯 وضع الاستهداف الجديد - القوات القادرة على الوصول:', friendlyUnitsInRange.length);

        // إنشاء بيانات مخصصة للعرض
        const customRecommendation: AITargetingResult = {
          targetUnitId: unitId,
          bestAttackerUnitId: friendlyUnitsInRange.length > 0 ? friendlyUnitsInRange[0].id : '',
          recommendedWeapon: 'artillery',
          successProbability: 85,
          estimatedDamage: 70,
          collateralRisk: 15,
          friendlyUnitsInRange: friendlyUnitsInRange
        };

        setAiRecommendation(customRecommendation);
        setShowTargetingUI(true);

        // حفظ بيانات الاستهداف للمحاكاة المتقدمة
        if (friendlyUnitsInRange.length > 0) {
          setCurrentTargetingData({
            targetUnitId: unitId,
            attackerUnitId: friendlyUnitsInRange[0].id,
            weaponType: 'artillery',
            accuracy: 85
          });
        }
      } else {
        alert('يمكن استهداف وحدات العدو فقط');
      }
    } else if (moveMode) {
      if (moveSourceUnitId === null) {
        if (clickedUnit.side === 'friendly') {
          setMoveSourceUnitId(unitId);
          alert(`تم اختيار ${clickedUnit.name} للتحريك. الرجاء اختيار الموقع الهدف على الخريطة.`);
        } else {
          alert('يمكن تحريك القوات الصديقة فقط');
        }
      } else {
        // Cancel current selection if clicking another unit
        setMoveSourceUnitId(null);
        alert('تم إلغاء التحريك. الرجاء اختيار وحدة جديدة للتحريك.');
      }
    } else {
      if (showCircleAnalysisSingle || showCircleAnalysisSingleWithTerrain || showCircleAnalysisSingleWithObstacles) {
        setSelectedUnitId(unitId);
        setShowCircleAnalysisFlash(false);
      } else {
        setSelectedUnitId(unitId);
        
        // Center map on selected unit
        if (clickedUnit.location.type === 'polygon') {
          const coords = clickedUnit.location.coordinates as number[][][];
          if (coords && coords[0] && coords[0][0]) {
            // Calculate center of polygon
            const centerLat = coords[0].reduce((sum, point) => sum + point[0], 0) / coords[0].length;
            const centerLng = coords[0].reduce((sum, point) => sum + point[1], 0) / coords[0].length;
            
            setMapCenter([centerLat, centerLng]);
            if (mapRef.current) {
              mapRef.current.setView([centerLat, centerLng], 9);
            }
          }
        }
      }
    }
  };
  
  // Handle map click
  const handleMapClick = (latlng: L.LatLng) => {
    if (moveMode && moveSourceUnitId !== null) {
      // Use the clicked position as the target for unit movement
      setMoveTargetPosition([latlng.lat, latlng.lng]);
      
      // Get the source unit and its associated equipment
      const sourceUnit = getUnitById(moveSourceUnitId);
      if (sourceUnit && sourceUnit.location.type === 'polygon') {
        // Get all equipment associated with this unit
        const unitEquipment = equipmentData.filter(eq => eq.unitId === moveSourceUnitId);
        
        // Calculate new positions for equipment relative to the new unit position
        const equipmentPositions = unitEquipment.map(eq => {
          const [offsetLat, offsetLng] = getStableEquipmentPosition(eq, sourceUnit);
          const coords = sourceUnit.location.coordinates as number[][][];
          const latDiff = offsetLat - coords[0][0][0];
          const lngDiff = offsetLng - coords[0][0][1];
          
          return {
            equipmentId: eq.id,
            newPosition: [latlng.lat + latDiff, latlng.lng + lngDiff] as [number, number]
          };
        });
        
        // Show battle scenario UI with move details
        setBattleScenario({
          type: 'attack',
          attackerUnitIds: [moveSourceUnitId],
          defenderUnitIds: [],
          targetPosition: [latlng.lat, latlng.lng],
          probability: 85,
          estimatedLosses: 5,
          equipmentPositions: equipmentPositions
        });
        setShowBattleScenarioUI(true);
      }
    } else {
      // Deselect if clicking empty space
      setSelectedUnitId(null);
    }
  };
  
  // Toggle targeting mode
  const toggleTargetingMode = () => {
    setTargetingMode(!targetingMode);
    // Reset move mode if active
    if (moveMode) setMoveMode(false);
    
    if (targetingMode) {
      setTargetUnitId(null);
      setAiRecommendation(null);
      setShowTargetingUI(false);
      setShowCircleAnalysisMultiple(false);
      setMultipleSelectedUnits([]);
    }
  };
  
  // Toggle move mode
  const toggleMoveMode = () => {
    setMoveMode(!moveMode);
    // Reset targeting mode if active
    if (targetingMode) setTargetingMode(false);
    
    if (moveMode) {
      setMoveSourceUnitId(null);
      setMoveTargetPosition(null);
      setShowBattleScenarioUI(false);
    }
  };

  // Toggle battle selection mode
  const toggleBattleSelectionMode = () => {
    setBattleSelectionMode(!battleSelectionMode);

    // إعادة تعيين الأوضاع الأخرى
    if (targetingMode) setTargetingMode(false);
    if (moveMode) setMoveMode(false);

    if (battleSelectionMode) {
      // إعادة تعيين الوحدات المختارة
      setSelectedFriendlyUnits([]);
      setSelectedEnemyUnits([]);
    }
  };

  // Start advanced battle simulation
  const startAdvancedBattleSimulation = () => {
    if (selectedFriendlyUnits.length === 0 || selectedEnemyUnits.length === 0) {
      alert('يجب اختيار وحدات من كلا الطرفين لبدء المحاكاة');
      return;
    }

    // بدء المحاكاة على الخريطة
    setMapBattleActive(true);
    setBattleSelectionMode(false);
  };

  // Clear unit selections
  const clearUnitSelections = () => {
    setSelectedFriendlyUnits([]);
    setSelectedEnemyUnits([]);
  };

  // Handle battle completion
  const handleBattleComplete = (result: {
    winner: 'friendly' | 'enemy' | 'draw';
    friendlyLosses: number;
    enemyLosses: number;
    duration: number;
    summary: string;
  }) => {
    setBattleResult(result);
    setMapBattleActive(false);
    // مسح الوحدات المختارة بعد انتهاء المعركة
    setSelectedFriendlyUnits([]);
    setSelectedEnemyUnits([]);
  };

  // Execute attack based on AI recommendation
  const executeAttack = () => {
    if (!aiRecommendation || !targetUnitId) return;
    
    const targetUnit = getUnitById(targetUnitId);
    const attackerUnit = getUnitById(aiRecommendation.bestAttackerUnitId);
    
    if (!targetUnit || !attackerUnit) return;
    
    // Get coordinates for animation
    const targetCoords = (targetUnit.location.coordinates as number[][][])[0][0];
    const attackerCoords = (attackerUnit.location.coordinates as number[][][])[0][0];
    
    // Start animation
    setAnimationConfig({
      source: [attackerCoords[0], attackerCoords[1]],
      target: [targetCoords[0], targetCoords[1]],
      isPlaying: true
    });
    
    // Hide targeting UI during animation
    setShowTargetingUI(false);
  };
  
  // Execute battle scenario
  const executeBattleScenario = () => {
    if (!battleScenario) return;
    
    if (battleScenario.type === 'attack' && battleScenario.attackerUnitIds.length > 0) {
      const attackerUnit = getUnitById(battleScenario.attackerUnitIds[0]);
      
      if (attackerUnit && battleScenario.targetPosition && battleScenario.targetPosition.length === 2) {
        const targetPos = battleScenario.targetPosition;
        
        // Update unit position
        setUnitsData(prevUnits => prevUnits.map(unit => {
          if (unit.id === attackerUnit.id && unit.location.type === 'polygon') {
            const coords = unit.location.coordinates as number[][][];
            const oldCenterLat = coords[0].reduce((sum, point) => sum + point[0], 0) / coords[0].length;
            const oldCenterLng = coords[0].reduce((sum, point) => sum + point[1], 0) / coords[0].length;
            const latDiff = targetPos[0] - oldCenterLat;
            const lngDiff = targetPos[1] - oldCenterLng;
            const newCoords = coords[0].map(point => [point[0] + latDiff, point[1] + lngDiff]);
            
            // Store the new position in localStorage for persistence
            const updatedUnit = {
              ...unit,
              location: {
                ...unit.location,
                coordinates: [newCoords]
              }
            };
            localStorage.setItem(`unit_position_${unit.id}`, JSON.stringify(updatedUnit));
            
            return updatedUnit;
          }
          return unit;
        }));
        
        // Update equipment positions
        if (battleScenario.equipmentPositions) {
          setEquipmentData(prevEq => prevEq.map(eq => {
            const eqPos = battleScenario.equipmentPositions!.find(e => e.equipmentId === eq.id);
            if (eqPos) {
              const updatedEq = {
                ...eq,
                unitId: attackerUnit.id
              };
              // Store equipment position in localStorage
              localStorage.setItem(`equipment_position_${eq.id}`, JSON.stringify(updatedEq));
              return updatedEq;
            }
            return eq;
          }));
        }
        
        setShowBattleScenarioUI(false);
        setMoveMode(false);
        setMoveSourceUnitId(null);
        setMoveTargetPosition(null);
        setBattleScenario(null);
        alert('تم نقل الوحدة والعتاد بنجاح!');
      }
    }
  };
  
  // Handle animation completion
  const handleAnimationComplete = () => {
    setAnimationConfig(null);
    setTargetingMode(false);
    setTargetUnitId(null);
    setAiRecommendation(null);
    setMoveMode(false);
    setMoveSourceUnitId(null);
    setMoveTargetPosition(null);
    setBattleScenario(null);
    
    // Simulate damage to enemy unit (this would be updated in a real system)
    alert('تم تنفيذ العملية بنجاح!');
  };
  
  // Manejadores para análisis espacial
  const handleToggleTerrainAnalysis = (show: boolean) => {
    setShowTerrainAnalysis(show);
  };
  
  const handleToggleCoverageAnalysis = (show: boolean) => {
    setShowCoverageAnalysis(show);
  };
  
  const handleToggleLOSAnalysis = (show: boolean) => {
    setShowLOSAnalysis(show);
  };
  
  const handleToggleRouteAnalysis = (show: boolean) => {
    setShowRouteAnalysis(show);
    if (!show) {
      setRouteStartUnitId(null);
      setRouteEndUnitId(null);
    }
  };
  
  const handleCalculateOptimalRoute = (startId: string, endId: string) => {
    setRouteStartUnitId(startId);
    setRouteEndUnitId(endId);
  };
  
  // Manejadores para simulación de combate
  const handleOpenBattleSimulator = () => {
    // عند النقر على زر محاكاة المعركة، نفتح واجهة اختيار القوات
    setShowBattleForceSelector(true);
  };
  
  const handleCloseBattleForceSelector = () => {
    setShowBattleForceSelector(false);
  };
  
  const handleBattleForceSelection = (selectedData: {
    friendlyUnits: string[];
    enemyUnits: string[];
    primaryWeapons: string[];
  }) => {
    // حفظ القوات المختارة
    setSelectedBattleForces(selectedData);
    
    // إغلاق واجهة الاختيار
    setShowBattleForceSelector(false);
    
    // بدء المحاكاة التلقائية
    startBattleSimulation(selectedData);
  };
  
  const startBattleSimulation = (selectedForces: {
    friendlyUnits: string[];
    enemyUnits: string[];
    primaryWeapons: string[];
  }) => {
    // تفعيل المحاكاة
    setAutoSimulationActive(true);
    
    // الحصول على الوحدات المختارة
    const selectedFriendlyUnits = selectedForces.friendlyUnits;
    const selectedEnemyUnits = selectedForces.enemyUnits;
    const selectedWeapons = selectedForces.primaryWeapons;
    
    // حساب قوة الهجوم بناءً على الوحدات والأسلحة المختارة
    const friendlyStrength = calculateForceStrength(selectedFriendlyUnits, selectedWeapons);
    const enemyStrength = calculateDefenseStrength(selectedEnemyUnits);
    
    // حساب احتمالية النجاح
    const totalStrength = friendlyStrength + enemyStrength;
    const winningProbability = Math.min(95, Math.max(5, Math.round((friendlyStrength / totalStrength) * 100)));
    
    // حساب الخسائر المتوقعة
    const personnelLosses = Math.round(50 + (100 - winningProbability) * 3);
    
    // حساب خسائر المعدات بناءً على الأسلحة المختارة
    const equipmentLosses: Record<string, number> = {
      tank: 0, apc: 0, artillery: 0, anti_air: 0, helicopter: 0,
      fighter: 0, drone: 0, missile: 0, radar: 0, logistics: 0,
      medical: 0, command: 0
    };
    
    // زيادة خسائر المعدات المستخدمة في المعركة
    selectedWeapons.forEach(weaponId => {
      const weapon = equipmentData.find(e => e.id === weaponId);
      if (weapon) {
        equipmentLosses[weapon.type] += 1 + Math.floor(Math.random() * 3);
      }
    });
    
    // إنشاء العوامل الرئيسية بناءً على القوات المختارة
    const keyFactors = [];
    
    if (friendlyStrength > enemyStrength * 1.5) {
      keyFactors.push('تفوق قوات الهجوم من حيث العدد والعتاد');
    } else if (enemyStrength > friendlyStrength * 1.5) {
      keyFactors.push('تفوق قوات الدفاع من حيث العدد والعتاد');
    }
    
    if (selectedWeapons.some(id => {
      const weapon = equipmentData.find(e => e.id === id);
      return weapon && (weapon.type === 'fighter' || weapon.type === 'helicopter');
    })) {
      keyFactors.push('استخدام القوة الجوية يعزز فرص النجاح');
    }
    
    if (selectedWeapons.some(id => {
      const weapon = equipmentData.find(e => e.id === id);
      return weapon && weapon.type === 'artillery';
    })) {
      keyFactors.push('الدعم المدفعي يمهد للهجوم البري');
    }
    
    // إنشاء نتيجة المحاكاة
    const simulationResult: BattleSimulationResult = {
      winningProbability,
      estimatedLosses: {
        personnel: personnelLosses,
        equipment: equipmentLosses as Record<any, number>
      },
      duration: 4 + Math.floor(Math.random() * 12),
      keyFactors
    };
    
    // تعيين نتيجة المحاكاة وتفعيل التأثيرات المرئية
    setBattleSimulationResult(simulationResult);
    setShowBattleSimulationEffects(true);
  };
  
  // حساب قوة الهجوم بناءً على الوحدات والأسلحة المختارة
  const calculateForceStrength = (unitIds: string[], weaponIds: string[]): number => {
    let strength = 0;
    
    // قوة الوحدات
    unitIds.forEach(id => {
      const unit = unitsData.find(u => u.id === id);
      if (unit) {
        strength += unit.personnelCount * (unit.readiness / 100);
      }
    });
    
    // قوة الأسلحة
    weaponIds.forEach(id => {
      const weapon = equipmentData.find(e => e.id === id);
      if (weapon) {
        const weaponMultiplier = 
          weapon.type === 'tank' ? 10 :
          weapon.type === 'artillery' ? 8 :
          weapon.type === 'fighter' || weapon.type === 'helicopter' ? 15 :
          weapon.type === 'missile' ? 12 : 5;
        
        strength += (weapon.firepower || 5) * weaponMultiplier;
      }
    });
    
    return strength;
  };
  
  // حساب قوة الدفاع
  const calculateDefenseStrength = (unitIds: string[]): number => {
    let strength = 0;
    
    unitIds.forEach(id => {
      const unit = unitsData.find(u => u.id === id);
      if (unit) {
        strength += unit.personnelCount * (unit.readiness / 100) * 1.2; // المدافع له ميزة
        
        // إضافة قوة معدات الوحدة
        unit.equipmentIds.forEach(eqId => {
          const eq = equipmentData.find(e => e.id === eqId);
          if (eq && eq.status === 'operational') {
            strength += (eq.firepower || 3) * 5;
          }
        });
      }
    });
    
    return strength;
  };
  
  const handleCloseBattleSimulator = () => {
    setShowBattleSimulator(false);
  };
  
  const handleSimulationComplete = (result: BattleSimulationResult) => {
    setBattleSimulationResult(result);
  };
  
  const handleBattleEffectsComplete = () => {
    setShowBattleSimulationEffects(false);
    setAutoSimulationActive(false);
    setSelectedBattleForces(null);
  };
  
  // Manejadores para planificación de escenarios
  const handleOpenScenarioPlanner = () => {
    setShowScenarioPlanner(true);
  };
  
  const handleCloseScenarioPlanner = () => {
    setShowScenarioPlanner(false);
  };
  
  const handleScenarioSelected = (scenario: any) => {
    setActiveScenario(scenario);
    setShowScenarioPlanner(false);
    
    // Aquí podríamos cargar el escenario en el mapa
    alert(`تم تحميل السيناريو: ${scenario.name}`);
  };
  
  // Handle search result click
  const handleSearchResultClick = (item: Unit | Equipment) => {
    // Check if it's a unit
    if ('commander' in item) {
      setSelectedUnitId(item.id);
      
      // Center map on selected unit
      if (item.location.type === 'polygon') {
        const coords = item.location.coordinates as number[][][];
        if (coords && coords[0] && coords[0][0]) {
          // Calculate center of polygon
          const centerLat = coords[0].reduce((sum, point) => sum + point[0], 0) / coords[0].length;
          const centerLng = coords[0].reduce((sum, point) => sum + point[1], 0) / coords[0].length;
          
          setMapCenter([centerLat, centerLng]);
          if (mapRef.current) {
            mapRef.current.setView([centerLat, centerLng], 9);
          }
        }
      }
    } else {
      // It's equipment, find its unit and center on that
      const unit = getUnitById(item.unitId || '');
      if (unit) {
        setSelectedUnitId(unit.id);
        
        if (unit.location.type === 'polygon') {
          const coords = unit.location.coordinates as number[][][];
          if (coords && coords[0] && coords[0][0]) {
            // Calculate center of polygon
            const centerLat = coords[0].reduce((sum, point) => sum + point[0], 0) / coords[0].length;
            const centerLng = coords[0].reduce((sum, point) => sum + point[1], 0) / coords[0].length;
            
            setMapCenter([centerLat, centerLng]);
            if (mapRef.current) {
              mapRef.current.setView([centerLat, centerLng], 9);
            }
          }
        }
      }
    }
  };
  
  // Toggle map layers visibility
  const toggleLayer = (layer: keyof typeof mapLayers) => {
    setMapLayers({
      ...mapLayers,
      [layer]: !mapLayers[layer]
    });
  };
  
  // Handle zoom control
  const handleZoom = (direction: 'in' | 'out') => {
    if (mapRef.current) {
      const currentZoom = mapRef.current.getZoom();
      direction === 'in' 
        ? mapRef.current.setZoom(currentZoom + 1)
        : mapRef.current.setZoom(currentZoom - 1);
    }
  };
  
  // Toggle satellite view
  const toggleSatelliteView = () => {
    setSatelliteView(!satelliteView);
  };
  
  // Select a satellite image
  const selectSatelliteImage = (index: number) => {
    // This would typically update the map tiles, but for this example we'll just toggle the state
    setSatelliteView(true);
    setShowMapGallery(false);
  };
  
  // Helper: حساب المسافة بين نقطتين جغرافيتين (Haversine)
  function getDistanceKm(lat1: number, lng1: number, lat2: number, lng2: number): number {
    const R = 6371; // نصف قطر الأرض كم
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  // جلب أنواع الأسلحة المتوفرة لدى القوات الصديقة
  const friendlyWeaponTypes = Array.from(new Set(
    equipmentData.filter(eq => eq.unitId && eq.capabilities.includes('attack') && eq.status === 'operational')
      .map(eq => eq.type)
  ));

  // تصفية القوات الصديقة بناءً على معايير المستخدم
  function getFilteredFriendlyUnitsForTarget(targetUnit: Unit) {
    if (targetUnit.location.type !== 'polygon') return [];
    const targetCoords = targetUnit.location.coordinates as number[][][];
    const targetCenterLat = targetCoords[0].reduce((sum, point) => sum + point[0], 0) / targetCoords[0].length;
    const targetCenterLng = targetCoords[0].reduce((sum, point) => sum + point[1], 0) / targetCoords[0].length;
    
    // Use the current unitsData state which includes moved positions
    return unitsData.filter(unit => {
      if (unit.side !== 'friendly' || unit.location.type !== 'polygon' || unit.readiness < targetingReadiness) return false;
      
      // Get current position of the unit (which may have been moved)
      const coords = unit.location.coordinates as number[][][];
      const unitCenterLat = coords[0].reduce((sum, point) => sum + point[0], 0) / coords[0].length;
      const unitCenterLng = coords[0].reduce((sum, point) => sum + point[1], 0) / coords[0].length;
      
      const distance = getDistanceKm(unitCenterLat, unitCenterLng, targetCenterLat, targetCenterLng);
      if (distance > targetingDistance) return false;
      
      // Check equipment at current position
      const eqs = equipmentData.filter(eq => eq.unitId === unit.id && eq.capabilities.includes('attack') && eq.status === 'operational' && (eq.range || 0) >= distance && (targetingWeaponType === 'all' || eq.type === targetingWeaponType));
      return eqs.length > 0;
    }).map(unit => {
      // Get current position of the unit
      const coords = unit.location.coordinates as number[][][];
      const unitCenterLat = coords[0].reduce((sum, point) => sum + point[0], 0) / coords[0].length;
      const unitCenterLng = coords[0].reduce((sum, point) => sum + point[1], 0) / coords[0].length;
      const distance = getDistanceKm(unitCenterLat, unitCenterLng, targetCenterLat, targetCenterLng);
      
      // Get equipment at current position
      const eqs = equipmentData.filter(eq => eq.unitId === unit.id && eq.capabilities.includes('attack') && eq.status === 'operational' && (eq.range || 0) >= distance && (targetingWeaponType === 'all' || eq.type === targetingWeaponType));
      const mainWeapon = eqs.sort((a, b) => (b.firepower || 0) - (a.firepower || 0))[0];
      
      return {
        ...unit,
        distance,
        mainWeapon
      };
    });
  }
  
  // Helper: حساب القوات الصديقة التي تقع ضمن دوائر التحليل لقوات العدو
  function getThreatenedFriendlyUnits() {
    const threatenedUnits: any[] = [];

    // جلب جميع وحدات العدو
    const enemyUnits = unitsData.filter(u => u.side === 'enemy' && u.location.type === 'polygon');

    // لكل وحدة صديقة، تحقق من وقوعها ضمن دوائر أي وحدة عدو
    unitsData.filter(u => u.side === 'friendly' && u.location.type === 'polygon').forEach(friendlyUnit => {
      const [friendlyLat, friendlyLng] = getUnitCenter(friendlyUnit);

      let isThreatenedBy: any[] = [];
      let minDist = Infinity;
      let nearestEnemy = null;
      let rangeType = '';

      // تحقق من كل وحدة عدو
      enemyUnits.forEach(enemyUnit => {
        const [enemyLat, enemyLng] = getUnitCenter(enemyUnit);
        const { blind, kill, nonkill } = getCircleRanges(enemyUnit);
        const distance = getDistanceKm(friendlyLat, friendlyLng, enemyLat, enemyLng);

        // تحديد نوع التهديد حسب الدائرة
        let currentRangeType = '';
        if (distance <= blind) {
          currentRangeType = 'عمى - خطر شديد';
          isThreatenedBy.push({ enemy: enemyUnit, distance, rangeType: currentRangeType, severity: 'critical' });
        } else if (distance <= kill) {
          currentRangeType = 'قتل - خطر عالي';
          isThreatenedBy.push({ enemy: enemyUnit, distance, rangeType: currentRangeType, severity: 'high' });
        } else if (distance <= nonkill) {
          currentRangeType = 'عدم قتل - خطر متوسط';
          isThreatenedBy.push({ enemy: enemyUnit, distance, rangeType: currentRangeType, severity: 'medium' });
        }

        // تتبع أقرب عدو
        if (distance < minDist) {
          minDist = distance;
          nearestEnemy = enemyUnit;
          rangeType = currentRangeType;
        }
      });

      // إضافة الوحدة إذا كانت مهددة
      if (isThreatenedBy.length > 0) {
        threatenedUnits.push({
          ...friendlyUnit,
          nearestEnemy,
          minDist,
          rangeType,
          threatenedBy: isThreatenedBy,
          maxSeverity: isThreatenedBy.reduce((max, threat) => {
            const severityOrder = { 'critical': 3, 'high': 2, 'medium': 1 };
            return severityOrder[threat.severity] > severityOrder[max] ? threat.severity : max;
          }, 'medium')
        });
      }
    });

    return threatenedUnits;
  }
  
  // قائمة معرفات الوحدات المهددة في التحليل المكاني
  const threatenedUnitIds = showSpatialAnalysisUI ? getThreatenedFriendlyUnits().map(u => u.id) : [];
  
  // دالة تفعيل تحليل الدوائر للوحدة المختارة
  const handleAnalyzeCirclesSingle = () => {
    setShowCircleAnalysisAll(false);
    setShowCircleAnalysisSingle(true);
    setShowCircleAnalysisFlash(true);
    setSelectedUnitId(null); // إلغاء التحديد السابق
  };
  // دالة تفعيل تحليل الدوائر للجميع
  const handleAnalyzeCirclesAll = () => {
    setShowCircleAnalysisSingle(false);
    setShowCircleAnalysisAll(true);
    setShowCircleAnalysisFlash(false);
    setSelectedUnitId(null);
  };

  // دالة تفعيل تحليل الدوائر مع التضاريس
  const handleAnalyzeCirclesSingleWithTerrain = () => {
    setShowCircleAnalysisSingle(false);
    setShowCircleAnalysisAll(false);
    setShowCircleAnalysisSingleWithObstacles(false);
    setShowCircleAnalysisSingleWithTerrain(true);
    setShowCircleAnalysisFlash(true);
    setSelectedUnitId(null);
  };

  // دالة تفعيل تحليل الدوائر مع العوائق
  const handleAnalyzeCirclesSingleWithObstacles = () => {
    setShowCircleAnalysisSingle(false);
    setShowCircleAnalysisAll(false);
    setShowCircleAnalysisSingleWithTerrain(false);
    setShowCircleAnalysisSingleWithObstacles(true);
    setShowCircleAnalysisFlash(true);
    setSelectedUnitId(null);
  };
  
  // دالة حساب مركز الوحدة
  const getUnitCenter = (unit: Unit): [number, number] => {
    if (unit.location.type === 'polygon') {
      const coords = unit.location.coordinates as number[][][];
      const centerLat = coords[0].reduce((sum, point) => sum + point[0], 0) / coords[0].length;
      const centerLng = coords[0].reduce((sum, point) => sum + point[1], 0) / coords[0].length;
      return [centerLat, centerLng];
    }
    return [0, 0];
  };
  // دالة جلب مدى السلاح الرئيسي للوحدة
  const getMainWeaponRange = (unit: Unit): number => {
    const eqs = equipmentData.filter(eq => eq.unitId === unit.id && eq.capabilities.includes('attack') && eq.status === 'operational');
    if (eqs.length === 0) return 5; // افتراضي لدبابة
    return Math.max(...eqs.map(eq => eq.range || 5));
  };
  // دالة جلب مدى الدوائر للوحدة حسب نوع السلاح
  const getCircleRanges = (unit: Unit) => {
    const eqs = equipmentData.filter(eq => eq.unitId === unit.id && eq.capabilities.includes('attack') && eq.status === 'operational');
    const mainType = eqs.length > 0 ? eqs[0].type : 'tank';
    const config = circleConfig || {};
    return config[mainType] || { blind: 1, kill: 5, nonkill: 10 };
  };
  // رسم دوائر التحليل حول وحدة
  const renderAnalysisCircles = (unit: Unit) => {
    const [centerLat, centerLng] = getUnitCenter(unit);
    const { blind, kill, nonkill } = getCircleRanges(unit);
    return [
      <Circle key={`blind-${unit.id}`} center={[centerLat, centerLng]} radius={blind * 1000} pathOptions={{ color: '#059669', fillColor: '#10b981', fillOpacity: 0.4, weight: 3 }} />,
      <Circle key={`kill-${unit.id}`} center={[centerLat, centerLng]} radius={kill * 1000} pathOptions={{ color: '#dc2626', fillColor: '#ef4444', fillOpacity: 0.35, weight: 3 }} />,
      <Circle key={`nonkill-${unit.id}`} center={[centerLat, centerLng]} radius={nonkill * 1000} pathOptions={{ color: '#d97706', fillColor: '#f59e0b', fillOpacity: 0.25, weight: 3, dashArray: '8 8' }} />
    ];
  };
  
  // عدة عوائق افتراضية (مصفوفة مضلعات)
  const obstaclePolygons = [
    {
      type: 'Feature',
      properties: {},
      geometry: {
        type: 'Polygon',
        coordinates: [
          [
            [36.0, 34.5],
            [36.3, 34.5],
            [36.3, 34.8],
            [36.0, 34.8],
            [36.0, 34.5]
          ]
        ]
      }
    },
    {
      type: 'Feature',
      properties: {},
      geometry: {
        type: 'Polygon',
        coordinates: [
          [
            [36.5, 34.7],
            [36.7, 34.7],
            [36.7, 34.9],
            [36.5, 34.9],
            [36.5, 34.7]
          ]
        ]
      }
    }
  ];
  // دمج العوائق في FeatureCollection
  const obstaclesCollection = {
    type: 'FeatureCollection',
    features: obstaclePolygons
  };
  // رسم جميع طبقات العوائق
  const renderObstacleLayer = () => (
    <>
      {obstaclePolygons.map((obs, idx) => (
        <Polygon
          key={`obstacle-${idx}`}
          positions={obs.geometry.coordinates[0].map((coord: number[]) => [coord[1], coord[0]])}
          pathOptions={{ color: '#6d28d9', fillColor: '#a78bfa', fillOpacity: 0.4, weight: 2, dashArray: '8 8' }}
        />
      ))}
    </>
  );
  // عدل دالة القص لاستخدام obstaclesCollection
  const renderClippedCircle = (radius: number, color: string, fillColor: string, keyPrefix: string, unit: Unit) => {
    const [centerLat, centerLng] = getUnitCenter(unit);
    const circle = turf.circle([centerLng, centerLat], radius, { steps: 80, units: 'kilometers' });
    let polygons: any[] = [];
    try {
      let remaining = circle;
      obstaclesCollection.features.forEach((obstacle: any) => {
        if (remaining && obstacle.geometry.type === 'Polygon') {
          const featureCollection = turf.featureCollection([remaining, obstacle]) as any;
          const clipped = turf.difference(featureCollection);
          if (clipped) remaining = clipped;
        }
      });
      polygons = remaining.geometry.type === 'Polygon' ? [remaining.geometry.coordinates] : remaining.geometry.coordinates;
    } catch (e) {
      polygons = [circle.geometry.coordinates];
    }
    return polygons.map((coords: any, idx: number) => (
      Array.isArray(coords[0]) && coords[0].length > 0 && coords[0][0].length === 2 ? (
        <Polygon
          key={`${keyPrefix}-${unit.id}-${idx}`}
          positions={coords[0].map((coord: number[]) => Array.isArray(coord) && coord.length === 2 ? [coord[1], coord[0]] : null).filter(Boolean) as [number, number][]}
          pathOptions={{ color, fillColor, fillOpacity: 0.3, weight: 2, dashArray: keyPrefix.includes('nonkill') ? '6 6' : undefined }}
        />
      ) : null
    ));
  };
  // عدل renderCircleWithObstacles لاستخدام renderClippedCircle الجديد
  const renderCircleWithObstacles = (unit: Unit) => {
    const { blind, kill, nonkill } = getCircleRanges(unit);
    const killCircles = renderClippedCircle(kill, '#dc2626', '#ef4444', 'kill-obstacle', unit);
    const nonkillCircles = renderClippedCircle(nonkill, '#d97706', '#f59e0b', 'nonkill-obstacle', unit);
    return [...killCircles, ...nonkillCircles];
  };

  // إنشاء FeatureCollection للتضاريس
  const createTerrainCollection = () => {
    const features = terrainData.map(terrain => ({
      type: 'Feature' as const,
      properties: { name: terrain.name, type: terrain.type },
      geometry: {
        type: 'Polygon' as const,
        coordinates: [terrain.coordinates.map(coord => [coord[1], coord[0]])] // عكس lat/lng لـ GeoJSON
      }
    }));
    
    return {
      type: 'FeatureCollection' as const,
      features
    };
  };

  // إنشاء FeatureCollection للعوائق
  const createObstaclesCollection = () => {
    const features = obstaclesData.map(obstacle => ({
      type: 'Feature' as const,
      properties: { name: obstacle.name, type: obstacle.type },
      geometry: {
        type: 'Polygon' as const,
        coordinates: [obstacle.coordinates.map(coord => [coord[1], coord[0]])] // عكس lat/lng لـ GeoJSON
      }
    }));
    
    return {
      type: 'FeatureCollection' as const,
      features
    };
  };

  // دالة رسم الدوائر مع قطع التضاريس
  const renderCircleWithTerrain = (unit: Unit) => {
    const [centerLat, centerLng] = getUnitCenter(unit);
    const { blind, kill, nonkill } = getCircleRanges(unit);
    const terrainCollection = createTerrainCollection();
    
    const renderClippedByTerrain = (radius: number, color: string, fillColor: string, keyPrefix: string) => {
      const circle = turf.circle([centerLng, centerLat], radius, { steps: 120, units: 'kilometers' });
      const polygonElements: JSX.Element[] = [];

      try {
        let clippedCircle: any = circle;
        console.log('🔍 بدء تحليل التضاريس للوحدة:', unit.name);
        console.log('📍 مركز الوحدة:', [centerLat, centerLng]);
        console.log('🏔️ عدد التضاريس:', terrainCollection.features.length);

        // قطع الدائرة بكل تضاريس واحدة تلو الأخرى
        terrainCollection.features.forEach((terrain, index) => {
          if (terrain.geometry.type === 'Polygon' && clippedCircle) {
            try {
              console.log(`🏔️ معالجة التضاريس ${index + 1}:`, terrain.properties?.name);

              // التحقق من التقاطع أولاً
              const featureCollection = turf.featureCollection([circle, terrain]);
              const intersection = turf.intersect(featureCollection);
              if (intersection) {
                console.log('✅ يوجد تقاطع مع التضاريس:', terrain.properties?.name);
                // قطع الجزء المتقاطع من الدائرة
                const featureCollection = turf.featureCollection([clippedCircle, terrain]) as any;
                const result = turf.difference(featureCollection);
                if (result) {
                  clippedCircle = result as any;
                  console.log('✂️ تم قطع الدائرة بنجاح');
                } else {
                  console.log('⚠️ فشل في قطع الدائرة - تم حذف الدائرة بالكامل');
                  clippedCircle = undefined;
                }
              } else {
                console.log('❌ لا يوجد تقاطع مع التضاريس:', terrain.properties?.name);
              }
            } catch (e) {
              console.warn('خطأ في قطع التضاريس:', e);
            }
          }
        });

        // رسم النتيجة النهائية
        if (clippedCircle && clippedCircle.geometry) {
          const geometry = clippedCircle.geometry;

          if (geometry.type === 'Polygon') {
            // مضلع واحد
            const coords = geometry.coordinates[0];
            if (coords && coords.length > 2) {
              polygonElements.push(
                <Polygon
                  key={`${keyPrefix}-clipped-${unit.id}`}
                  positions={coords.map((coord: number[]) => [coord[1], coord[0]])}
                  pathOptions={{
                    color,
                    fillColor,
                    fillOpacity: keyPrefix === 'blind' ? 0.4 : keyPrefix === 'kill' ? 0.35 : 0.25,
                    weight: 3,
                    dashArray: keyPrefix === 'nonkill' ? '8 8' : undefined
                  }}
                />
              );
            }
          } else if (geometry.type === 'MultiPolygon') {
            // عدة مضلعات
            (geometry.coordinates as any).forEach((polygonCoords: any, polyIndex: number) => {
              const coords = polygonCoords[0];
              if (coords && coords.length > 2) {
                polygonElements.push(
                  <Polygon
                    key={`${keyPrefix}-clipped-${unit.id}-${polyIndex}`}
                    positions={coords.map((coord: number[]) => [coord[1], coord[0]])}
                    pathOptions={{
                      color,
                      fillColor,
                      fillOpacity: keyPrefix === 'blind' ? 0.4 : keyPrefix === 'kill' ? 0.35 : 0.25,
                      weight: 3,
                      dashArray: keyPrefix === 'nonkill' ? '8 8' : undefined
                    }}
                  />
                );
              }
            });
          }
        }

      } catch (e) {
        console.warn('خطأ في معالجة قطع التضاريس:', e);
        // في حالة خطأ، ارسم الدائرة الكاملة
        const coords = circle.geometry.coordinates[0];
        polygonElements.push(
          <Polygon
            key={`${keyPrefix}-error-${unit.id}`}
            positions={coords.map((coord: number[]) => [coord[1], coord[0]])}
            pathOptions={{
              color,
              fillColor,
              fillOpacity: keyPrefix === 'blind' ? 0.4 : keyPrefix === 'kill' ? 0.35 : 0.25,
              weight: 3,
              dashArray: keyPrefix === 'nonkill' ? '8 8' : undefined
            }}
          />
        );
      }

      return polygonElements;
    };

    const blindCircles = renderClippedByTerrain(blind, '#059669', '#10b981', 'blind');
    const killCircles = renderClippedByTerrain(kill, '#dc2626', '#ef4444', 'kill');
    const nonkillCircles = renderClippedByTerrain(nonkill, '#d97706', '#f59e0b', 'nonkill');
    
    return [...blindCircles, ...killCircles, ...nonkillCircles];
  };

  // دالة رسم الدوائر مع قطع العوائق
  const renderCircleWithObstaclesAdvanced = (unit: Unit) => {
    const [centerLat, centerLng] = getUnitCenter(unit);
    const { blind, kill, nonkill } = getCircleRanges(unit);
    const obstaclesCollection = createObstaclesCollection();
    
    const renderClippedByObstacles = (radius: number, color: string, fillColor: string, keyPrefix: string) => {
      const circle = turf.circle([centerLng, centerLat], radius, { steps: 120, units: 'kilometers' });
      const polygonElements: JSX.Element[] = [];

      try {
        let clippedCircle: any = circle;

        // قطع الدائرة بكل عائق واحد تلو الآخر
        obstaclesCollection.features.forEach((obstacle) => {
          if (obstacle.geometry.type === 'Polygon' && clippedCircle) {
            try {
              // قطع الجزء المتقاطع من الدائرة
              const featureCollection = turf.featureCollection([clippedCircle, obstacle]) as any;
              const result = turf.difference(featureCollection);
              if (result) {
                clippedCircle = result as any;
              }
            } catch (e) {
              console.warn('خطأ في قطع العوائق:', e);
            }
          }
        });

        // رسم النتيجة النهائية
        if (clippedCircle && clippedCircle.geometry) {
          const geometry = clippedCircle.geometry;

          if (geometry.type === 'Polygon') {
            // مضلع واحد
            const coords = geometry.coordinates[0];
            if (coords && coords.length > 2) {
              polygonElements.push(
                <Polygon
                  key={`${keyPrefix}-clipped-${unit.id}`}
                  positions={coords.map((coord: number[]) => [coord[1], coord[0]])}
                  pathOptions={{
                    color,
                    fillColor,
                    fillOpacity: keyPrefix === 'blind' ? 0.4 : keyPrefix === 'kill' ? 0.35 : 0.25,
                    weight: 3,
                    dashArray: keyPrefix === 'nonkill' ? '8 8' : undefined
                  }}
                />
              );
            }
          } else if (geometry.type === 'MultiPolygon') {
            // عدة مضلعات
            (geometry.coordinates as any).forEach((polygonCoords: any, polyIndex: number) => {
              const coords = polygonCoords[0];
              if (coords && coords.length > 2) {
                polygonElements.push(
                  <Polygon
                    key={`${keyPrefix}-clipped-${unit.id}-${polyIndex}`}
                    positions={coords.map((coord: number[]) => [coord[1], coord[0]])}
                    pathOptions={{
                      color,
                      fillColor,
                      fillOpacity: keyPrefix === 'blind' ? 0.4 : keyPrefix === 'kill' ? 0.35 : 0.25,
                      weight: 3,
                      dashArray: keyPrefix === 'nonkill' ? '8 8' : undefined
                    }}
                  />
                );
              }
            });
          }
        }

      } catch (e) {
        console.warn('خطأ في معالجة قطع العوائق:', e);
        // في حالة خطأ، ارسم الدائرة الكاملة
        const coords = circle.geometry.coordinates[0];
        polygonElements.push(
          <Polygon
            key={`${keyPrefix}-error-${unit.id}`}
            positions={coords.map((coord: number[]) => [coord[1], coord[0]])}
            pathOptions={{
              color,
              fillColor,
              fillOpacity: keyPrefix === 'blind' ? 0.4 : keyPrefix === 'kill' ? 0.35 : 0.25,
              weight: 3,
              dashArray: keyPrefix === 'nonkill' ? '8 8' : undefined
            }}
          />
        );
      }

      return polygonElements;
    };

    const blindCircles = renderClippedByObstacles(blind, '#059669', '#10b981', 'blind');
    const killCircles = renderClippedByObstacles(kill, '#dc2626', '#ef4444', 'kill');
    const nonkillCircles = renderClippedByObstacles(nonkill, '#d97706', '#f59e0b', 'nonkill');
    
    return [...blindCircles, ...killCircles, ...nonkillCircles];
  };

  // دالة رسم طبقة التضاريس
  const renderTerrainLayer = () => {
    if (!mapLayers.terrain) return null;
    
    return terrainData.map(terrain => (
      <Polygon
        key={`terrain-${terrain.id}`}
        positions={terrain.coordinates.map(coord => [coord[0], coord[1]] as [number, number])}
        pathOptions={{
          color: terrain.color,
          fillColor: terrain.color,
          fillOpacity: 0.4,
          weight: 2,
          opacity: 0.8
        }}
      >
        <Popup>
          <div className="p-2">
            <h3 className="font-bold">{terrain.name}</h3>
            <p>النوع: {terrain.type}</p>
            <p>الارتفاع: {terrain.elevation} متر</p>
          </div>
        </Popup>
      </Polygon>
    ));
  };

  // دالة رسم طبقة العوائق
  const renderObstaclesLayer = () => {
    if (!mapLayers.obstacles) return null;
    
    return obstaclesData.map(obstacle => (
      <Polygon
        key={`obstacle-${obstacle.id}`}
        positions={obstacle.coordinates.map(coord => [coord[0], coord[1]] as [number, number])}
        pathOptions={{
          color: obstacle.color,
          fillColor: obstacle.color,
          fillOpacity: obstacle.type === 'trench' ? 0.6 : 0.7,
          weight: 3,
          opacity: 1,
          dashArray: obstacle.type === 'wire_fence' ? '8 4' : undefined
        }}
      >
        <Popup>
          <div className="p-2">
            <h3 className="font-bold">{obstacle.name}</h3>
            <p>النوع: {obstacle.type}</p>
            {obstacle.height && <p>الارتفاع: {obstacle.height} متر</p>}
            {obstacle.depth && <p>العمق: {obstacle.depth} متر</p>}
          </div>
        </Popup>
      </Polygon>
    ));
  };
  
  return (
    <div className="h-full relative flex">
      {/* Main Map Container */}
      <div className="h-full flex-1 relative">
        <MapContainer
          center={mapCenter}
          zoom={zoomLevel}
          style={{ height: '100%', width: '100%' }}
          ref={mapRef}
        >
          {/* Base Map Layer */}
          {satelliteView ? (
            <div className="absolute inset-0 z-0">
              <img 
                src={mapImages[0]} 
                alt="Military satellite view"
                className="w-full h-full object-cover"
                style={{ opacity: 0.9 }}
              />
            </div>
          ) : (
            <TileLayer
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            />
          )}
          
          {/* Map Controller for Events */}
          <MapController onMapClick={handleMapClick} />
          
          {/* طبقة التضاريس */}
          {renderTerrainLayer()}
          
          {/* طبقة العوائق */}
          {renderObstaclesLayer()}
          
          {/* Render Units as Polygons */}
          {unitsData.map(unit => {
            // Skip if layer is disabled
            if ((unit.side === 'friendly' && !mapLayers.friendly) || 
                (unit.side === 'enemy' && !mapLayers.enemy)) {
              return null;
            }
            
            if (unit.location.type === 'polygon') {
              const coords = unit.location.coordinates as number[][][];
              const positions = coords[0].map(coord => [coord[0], coord[1]] as [number, number]);
              
              return (
                <Polygon
                  key={unit.id}
                  positions={positions}
                  pathOptions={{
                    color: unit.side === 'friendly' ? '#1d4ed8' : '#b91c1c',
                    fillColor: unit.side === 'friendly' ? '#3b82f6' : '#ef4444',
                    fillOpacity: selectedUnitId === unit.id ? 0.7 : 0.5,
                    weight: selectedUnitId === unit.id ? 3 : 2
                  }}
                  eventHandlers={{
                    click: () => handleUnitClick(unit.id)
                  }}
                >
                  <Popup>
                    <div className="p-2">
                      <h3 className="font-bold">{unit.name}</h3>
                      <p>القائد: {unit.commander}</p>
                      <p>الأفراد: {unit.personnelCount}</p>
                      <p>الحالة: {
                        unit.status === 'active' ? 'نشط' : 
                        unit.status === 'reserve' ? 'احتياط' : 'متضرر'
                      }</p>
                      <p>الجاهزية: {unit.readiness}%</p>
                    </div>
                  </Popup>
                </Polygon>
              );
            }
            return null;
          })}
          
          {/* Render Unit Icons */}
          {unitsData.map(unit => {
            // Skip if layer is disabled
            if ((unit.side === 'friendly' && !mapLayers.friendly) || 
                (unit.side === 'enemy' && !mapLayers.enemy)) {
              return null;
            }
            if (unit.location.type === 'polygon') {
              const coords = unit.location.coordinates as number[][][];
              // Calculate center of polygon for icon placement
              const centerLat = coords[0].reduce((sum, point) => sum + point[0], 0) / coords[0].length;
              const centerLng = coords[0].reduce((sum, point) => sum + point[1], 0) / coords[0].length;
              
              // تحديد ما إذا كانت الوحدة مختارة أو مستهدفة في محاكاة المعركة
              const isSelectedForBattle = selectedBattleForces?.friendlyUnits.includes(unit.id) || false;
              const isTargetForBattle = selectedBattleForces?.enemyUnits.includes(unit.id) || false;

              // تحديد ما إذا كانت الوحدة مختارة للمحاكاة المتقدمة
              const isSelectedForAdvancedBattle =
                (unit.side === 'friendly' && selectedFriendlyUnits.includes(unit.id)) ||
                (unit.side === 'enemy' && selectedEnemyUnits.includes(unit.id));

              // تحديد ما إذا كانت الوحدة في معركة نشطة
              const isInActiveBattle = mapBattleActive && isSelectedForAdvancedBattle;
              
              // إذا كانت الوحدة مهددة أضف فلاش
              const isThreatened = threatenedUnitIds.includes(unit.id);
              const shouldShowThreatenedFlash = isThreatened && showThreatenedUnitsFlash;
              
              // تحديد الفئة والحجم بناءً على حالة الوحدة
              let iconClass = unit.side === 'friendly' ? 'friendly' : 'enemy';
              let iconSize = [30, 30];
              let iconAnchor = [15, 15];
              
              if (isSelectedForBattle) {
                iconClass += ' selected-unit';
                iconSize = [36, 36];
                iconAnchor = [18, 18];
              } else if (isTargetForBattle) {
                iconClass += ' target-unit';
                iconSize = [36, 36];
                iconAnchor = [18, 18];
              } else if (isInActiveBattle) {
                iconClass += ' battle-active';
                iconSize = [45, 45];
                iconAnchor = [22.5, 22.5];
              } else if (isSelectedForAdvancedBattle) {
                iconClass += ' advanced-battle-selected';
                iconSize = [40, 40];
                iconAnchor = [20, 20];
              }
              
              // إضافة فئة النبض إذا كانت الوحدة مهددة
              const pulseClass = isThreatened ? 
                ` pulse-threat ${unit.side === 'friendly' ? 'pulse-blue' : 'pulse-red'}` : 
                '';
              
              // إضافة فئة النبض إذا كانت الوحدة مختارة للمعركة
              const battleClass = isSelectedForBattle ? 
                ' battle-selected' : 
                (isTargetForBattle ? ' battle-target' : '');
              
              return (
                <Marker
                  key={`icon-${unit.id}`}
                  position={[centerLat, centerLng]}
                  icon={L.divIcon({
                    html: `<div class="unit-icon-container${pulseClass}${battleClass}"><div class="unit-icon ${iconClass}">${unit.type.charAt(0).toUpperCase()}</div></div>`,
                    className: 'leaflet-div-icon',
                    iconSize: iconSize as [number, number],
                    iconAnchor: iconAnchor as [number, number]
                  })}
                  eventHandlers={{
                    click: () => handleUnitClick(unit.id)
                  }}
                />
              );
            }
            return null;
          })}

          {/* فلاش أحمر للوحدات المهددة */}
          {showThreatenedUnitsFlash && threatenedUnitIds.map(unitId => {
            const unit = getUnitById(unitId);
            if (!unit || unit.location.type !== 'polygon') return null;

            const [centerLat, centerLng] = getUnitCenter(unit);

            return (
              <Circle
                key={`threatened-flash-${unitId}`}
                center={[centerLat, centerLng]}
                radius={2000} // 2 كم
                pathOptions={{
                  color: '#dc2626',
                  fillColor: '#ef4444',
                  fillOpacity: 0.6,
                  weight: 4,
                  className: 'threatened-unit-flash'
                }}
              />
            );
          })}

          {/* Render Equipment */}
          {mapLayers.equipment && equipmentData.map(eq => {
            // Get unit to determine position
            const unitId = eq.unitId;
            if (!unitId) return null;
            
            const unit = getUnitById(unitId);
            if (!unit || unit.location.type !== 'polygon') return null;
            
            // Skip if unit's layer is disabled
            if ((unit.side === 'friendly' && !mapLayers.friendly) || 
                (unit.side === 'enemy' && !mapLayers.enemy)) {
              return null;
            }
            
            // Get stable position for equipment
            const [offsetLat, offsetLng] = getStableEquipmentPosition(eq, unit);
            
            return (
              (typeof offsetLat === 'number' && typeof offsetLng === 'number') ? (
                <Marker
                  key={`eq-${eq.id}`}
                  position={[offsetLat, offsetLng] as [number, number]}
                  icon={L.divIcon({
                    html: `<div class="equipment-icon-container ${unit.side}">${eq.type.charAt(0).toUpperCase()}</div>`,
                    className: 'leaflet-div-icon',
                    iconSize: [24, 24],
                    iconAnchor: [12, 12]
                  })}
                >
                  <Popup>
                    <div className="p-2">
                      <h3 className="font-bold">{eq.name}</h3>
                      <p>النوع: {eq.type}</p>
                      <p>الكمية: {eq.quantity}</p>
                      <p>الحالة: {
                        eq.status === 'operational' ? 'جاهز' : 
                        eq.status === 'maintenance' ? 'صيانة' : 'متضرر'
                      }</p>
                    </div>
                  </Popup>
                </Marker>
              ) : null
            );
          })}
          
          {/* Move Target Marker */}
          {moveMode && moveTargetPosition && (
            <Marker
              position={moveTargetPosition}
              icon={L.divIcon({
                html: `<div style="
                  width: 20px; 
                  height: 20px; 
                  border-radius: 50%; 
                  background-color: rgba(0,128,0,0.6); 
                  border: 2px solid white;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  animation: pulse 1.5s infinite;
                "></div>`,
                className: 'leaflet-div-icon',
                iconSize: [20, 20],
                iconAnchor: [10, 10]
              })}
            />
          )}
          
          {/* Battle Animation */}
          {animationConfig && animationConfig.isPlaying && (
            <BattleAnimation
              sourceLatLng={animationConfig.source}
              targetLatLng={animationConfig.target}
              onAnimationComplete={handleAnimationComplete}
            />
          )}
          
          {/* رسم دوائر التحليل للوحدة المختارة */}
          {showCircleAnalysisSingle && selectedUnitId && (
            renderAnalysisCircles(getUnitById(selectedUnitId)!)
          )}
          {/* رسم دوائر التحليل لجميع الوحدات */}
          {showCircleAnalysisAll && unitsData.map(unit => renderAnalysisCircles(unit))}

          {/* رسم دوائر التحليل للقوات المتعددة المختارة */}
          {showCircleAnalysisMultiple && multipleSelectedUnits.map(unitId => {
            const unit = getUnitById(unitId);
            console.log('🔵 رسم دائرة للوحدة:', unit?.name, 'معرف:', unitId);
            return unit ? renderAnalysisCircles(unit) : null;
          })}
          
          {/* رسم دوائر التحليل مع التضاريس للوحدة المختارة */}
          {showCircleAnalysisSingleWithTerrain && selectedUnitId && (
            renderCircleWithTerrain(getUnitById(selectedUnitId)!)
          )}
          
          {/* رسم دوائر التحليل مع العوائق للوحدة المختارة */}
          {showCircleAnalysisSingleWithObstacles && selectedUnitId && (
            renderCircleWithObstaclesAdvanced(getUnitById(selectedUnitId)!)
          )}
          
          {/* رسم طبقة العوائق الافتراضية */}
          {renderObstacleLayer()}
          {/* رسم الدوائر مع العوائق للوحدة المختارة */}
          {showEditCirclesSingleWithObstacles && selectedUnitId && (
            renderCircleWithObstacles(getUnitById(selectedUnitId)!)
          )}
          {/* رسم الدوائر مع العوائق لجميع الوحدات */}
          {showEditCirclesAllWithObstacles && unitsData.map(unit => renderCircleWithObstacles(unit))}
          
          {/* Componentes de análisis espacial avanzado */}
          {showTerrainAnalysis && (
            <TerrainAnalysisLayer 
              units={unitsData}
              selectedUnitId={selectedUnitId}
              showElevation={true}
              showCoverage={true}
              showLOS={false}
            />
          )}
          
          {showRouteAnalysis && (
            <RouteAnalysisLayer 
              units={unitsData}
              startUnitId={routeStartUnitId}
              endUnitId={routeEndUnitId}
              showRouteAnalysis={showRouteAnalysis}
            />
          )}
          
          {/* Componentes de simulación de combate y planificación de escenarios */}
          {showBattleSimulator && (
            <BattleSimulator 
              onClose={handleCloseBattleSimulator}
              onSimulationComplete={handleSimulationComplete}
            />
          )}
          
          {/* واجهة اختيار القوات للمعركة */}
          {showBattleForceSelector && (
            <BattleForceSelector
              friendlyUnits={unitsData.filter(u => u.side === 'friendly')}
              enemyUnits={unitsData.filter(u => u.side === 'enemy')}
              equipment={equipmentData}
              onConfirm={handleBattleForceSelection}
              onCancel={handleCloseBattleForceSelector}
            />
          )}
          
          {/* تأثيرات محاكاة المعركة التلقائية */}
          {showBattleSimulationEffects && (
            <BattleSimulationEffects
              simulationResult={battleSimulationResult}
              isActive={autoSimulationActive}
              selectedForces={selectedBattleForces}
              attackerCoordinates={unitsData.filter(u => selectedBattleForces?.friendlyUnits.includes(u.id) || u.side === 'friendly').map(u => {
                const coords = u.location.type === 'point' 
                  ? u.location.coordinates[0] as [number, number]
                  : [(u.location.coordinates as number[][][])[0][0][0], (u.location.coordinates as number[][][])[0][0][1]];
                return [coords[0], coords[1]] as [number, number];
              })}
              defenderCoordinates={unitsData.filter(u => selectedBattleForces?.enemyUnits.includes(u.id) || u.side === 'enemy').map(u => {
                const coords = u.location.type === 'point' 
                  ? u.location.coordinates[0] as [number, number]
                  : [(u.location.coordinates as number[][][])[0][0][0], (u.location.coordinates as number[][][])[0][0][1]];
                return [coords[0], coords[1]] as [number, number];
              })}
              onEffectsComplete={handleBattleEffectsComplete}
            />
          )}
          
          {showScenarioPlanner && (
            <ScenarioPlanner 
              onClose={handleCloseScenarioPlanner}
              onScenarioSelected={handleScenarioSelected}
            />
          )}
          
          {/* Real Battle Simulation - moved inside MapContainer */}
          {showRealBattleSimulation && (
            <RealBattleSimulation onClose={() => setShowRealBattleSimulation(false)} />
          )}
        </MapContainer>
        
        {/* Map Controls */}
        <MapControls 
          mapLayers={mapLayers}
          onToggleLayer={toggleLayer}
          onZoom={handleZoom}
          onToggleSatelliteView={toggleSatelliteView}
          onToggleMapGallery={() => setShowMapGallery(!showMapGallery)}
          satelliteView={satelliteView}
          targetingMode={targetingMode}
          moveMode={moveMode}
          onToggleTargetingMode={toggleTargetingMode}
          onToggleMoveMode={toggleMoveMode}
          // Propiedades para análisis espacial avanzado
          onToggleTerrainAnalysis={() => handleToggleTerrainAnalysis(!showTerrainAnalysis)}
          onToggleRouteAnalysis={() => handleToggleRouteAnalysis(!showRouteAnalysis)}
          showTerrainAnalysis={showTerrainAnalysis}
          showRouteAnalysis={showRouteAnalysis}
          // Propiedades para simulación de combate y planificación de escenarios
          onOpenBattleSimulator={handleOpenBattleSimulator}
          onOpenScenarioPlanner={handleOpenScenarioPlanner}
          onSpatialAnalysisClick={() => setShowSpatialAnalysisUI(true)}
          onAnalyzeCirclesSingle={handleAnalyzeCirclesSingle}
          onAnalyzeCirclesAll={handleAnalyzeCirclesAll}
          onAnalyzeCirclesSingleWithTerrain={handleAnalyzeCirclesSingleWithTerrain}
          onAnalyzeCirclesSingleWithObstacles={handleAnalyzeCirclesSingleWithObstacles}
          onOpenCircleConfig={() => setShowCircleConfig(true)}
          onEditCirclesSingleWithObstacles={() => setShowEditCirclesSingleWithObstacles(true)}
          onEditCirclesAllWithObstacles={() => setShowEditCirclesAllWithObstacles(true)}
          // Advanced Battle Simulation
          battleSelectionMode={battleSelectionMode}
          selectedFriendlyUnits={selectedFriendlyUnits}
          selectedEnemyUnits={selectedEnemyUnits}
          onToggleBattleSelectionMode={toggleBattleSelectionMode}
          onStartAdvancedBattleSimulation={startAdvancedBattleSimulation}
          onClearUnitSelections={clearUnitSelections}
        />
        
        {/* Search Bar */}
        <SearchBar
          units={unitsData}
          equipment={equipmentData}
          onResultClick={handleSearchResultClick}
        />

        {/* Battle Selection UI */}
        {(battleSelectionMode || mapBattleActive) && (
          <div className="absolute top-4 right-4 z-[9999] bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl p-6 max-w-md">
            <div className="mb-4">
              <h3 className="text-lg font-bold font-heliopolis text-neutral-90 mb-2">
                {mapBattleActive ? '⚔️ معركة نشطة' : '🎯 اختيار وحدات للمعركة'}
              </h3>
              <p className="text-sm text-neutral-70 font-heliopolis">
                {mapBattleActive
                  ? 'المعركة جارية بين الوحدات المختارة...'
                  : 'انقر على الوحدات في الخريطة لاختيارها للمحاكاة'
                }
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="bg-gradient-to-r from-success/10 to-success/5 rounded-xl p-3">
                <h4 className="font-bold text-success mb-2 font-heliopolis text-sm">
                  القوات الصديقة
                </h4>
                <div className="text-2xl font-bold text-success mb-1">
                  {selectedFriendlyUnits.length}
                </div>
                <div className="text-xs text-neutral-60">
                  {selectedFriendlyUnits.length > 0 && (
                    <div className="max-h-20 overflow-y-auto">
                      {selectedFriendlyUnits.map(unitId => {
                        const unit = getUnitById(unitId);
                        return unit ? (
                          <div key={unitId} className="text-xs truncate">
                            {unit.name}
                          </div>
                        ) : null;
                      })}
                    </div>
                  )}
                </div>
              </div>

              <div className="bg-gradient-to-r from-error/10 to-error/5 rounded-xl p-3">
                <h4 className="font-bold text-error mb-2 font-heliopolis text-sm">
                  القوات المعادية
                </h4>
                <div className="text-2xl font-bold text-error mb-1">
                  {selectedEnemyUnits.length}
                </div>
                <div className="text-xs text-neutral-60">
                  {selectedEnemyUnits.length > 0 && (
                    <div className="max-h-20 overflow-y-auto">
                      {selectedEnemyUnits.map(unitId => {
                        const unit = getUnitById(unitId);
                        return unit ? (
                          <div key={unitId} className="text-xs truncate">
                            {unit.name}
                          </div>
                        ) : null;
                      })}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {!mapBattleActive && (
              <div className="flex space-x-2 space-x-reverse">
                <button
                  onClick={startAdvancedBattleSimulation}
                  disabled={selectedFriendlyUnits.length === 0 || selectedEnemyUnits.length === 0}
                  className="flex-1 bg-gradient-primary text-white px-4 py-2 rounded-xl font-heliopolis font-medium shadow-lg transition-all duration-200 hover:shadow-xl hover:scale-105 disabled:bg-neutral-40 disabled:cursor-not-allowed disabled:hover:scale-100"
                >
                  🚀 بدء المحاكاة
                </button>

                <button
                  onClick={clearUnitSelections}
                  className="px-4 py-2 bg-neutral-20 text-neutral-70 rounded-xl font-heliopolis font-medium hover:bg-neutral-30 transition-colors"
                >
                  مسح
                </button>

                <button
                  onClick={toggleBattleSelectionMode}
                  className="px-4 py-2 bg-error text-white rounded-xl font-heliopolis font-medium hover:bg-error/80 transition-colors"
                >
                  إلغاء
                </button>
              </div>
            )}

            {mapBattleActive && (
              <div className="text-center">
                <div className="bg-gradient-to-r from-warning/20 to-error/20 rounded-xl p-4 mb-4">
                  <div className="flex items-center justify-center space-x-2 space-x-reverse mb-2">
                    <div className="w-3 h-3 bg-error rounded-full animate-pulse"></div>
                    <span className="font-bold text-error font-heliopolis">معركة نشطة</span>
                    <div className="w-3 h-3 bg-error rounded-full animate-pulse"></div>
                  </div>
                  <p className="text-sm text-neutral-70 font-heliopolis">
                    ستظهر النتائج عند انتهاء المعركة
                  </p>
                </div>
              </div>
            )}
          </div>
        )}
        
        {/* Targeting UI */}
        {showTargetingUI && aiRecommendation && targetUnitId && (
          (() => {
            const targetUnit = getUnitById(targetUnitId);
            const friendlyUnitsInRange = aiRecommendation.friendlyUnitsInRange || [];
            return (
              <div className="absolute inset-x-0 bottom-0 z-[9999] rounded-t-lg bg-white p-4 shadow-lg" style={{zIndex: 9999}}>
                <div className="mb-4 flex items-center justify-between">
                  <h3 className="text-lg font-bold">القوات القادرة على الاستهداف - محدث</h3>
                  <button
                    onClick={() => setShowTargetingUI(false)}
                    className="rounded-full p-1 hover:bg-gray-100"
                  >
                    <X size={20} />
                  </button>
                </div>
                <div className="mb-4">
                  <div className="font-medium mb-2">الهدف: {targetUnit?.name}</div>
                  <div className="text-sm text-gray-500 mb-2">عدد الأفراد: {targetUnit?.personnelCount}</div>
                  <div className="text-sm text-blue-600 mb-2">
                    القوات الصديقة القادرة على الوصول: {friendlyUnitsInRange.length}
                  </div>
                </div>

                <div className="mb-4">
                  <h4 className="font-medium mb-2">القوات القادرة على الوصول للهدف ({friendlyUnitsInRange.length})</h4>
                  <div className="max-h-40 overflow-y-auto">
                    {friendlyUnitsInRange.length === 0 ? (
                      <div className="text-gray-500 text-sm">لا توجد قوات قادرة على الوصول للهدف</div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {friendlyUnitsInRange.map(unit => {
                          const [unitLat, unitLng] = getUnitCenter(unit);
                          const [targetLat, targetLng] = getUnitCenter(targetUnit!);
                          const distance = getDistanceKm(unitLat, unitLng, targetLat, targetLng);
                          const { blind, kill, nonkill } = getCircleRanges(unit);

                          // تحديد نوع الدائرة التي يقع فيها الهدف
                          let rangeType = '';
                          if (distance <= blind) rangeType = 'عمى';
                          else if (distance <= kill) rangeType = 'قتل';
                          else if (distance <= nonkill) rangeType = 'عدم قتل';

                          return (
                            <div key={unit.id} className="flex items-center justify-between p-2 bg-green-50 rounded border border-green-200 hover:bg-green-100 cursor-pointer transition-colors"
                                 onClick={() => {
                                   // إظهار دائرة التحليل للوحدة المختارة
                                   setSelectedUnitId(unit.id);
                                   setShowCircleAnalysisSingle(true);
                                   console.log('🎯 تم اختيار الوحدة لإظهار دائرتها:', unit.name);
                                 }}>
                              <div className="flex-1">
                                <div className="font-medium text-sm">{unit.name}</div>
                                <div className="text-xs text-gray-500">المسافة: {distance.toFixed(1)} كم</div>
                                <div className="text-xs text-green-600">نطاق: {rangeType}</div>
                                <div className="text-xs text-blue-600 mt-1">انقر للتفاعل مع الوحدة</div>
                              </div>
                              <div className="flex items-center gap-2">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    // إظهار دائرة التحليل للوحدة المختارة
                                    setSelectedUnitId(unit.id);
                                    setShowCircleAnalysisSingle(true);
                                    console.log('🔵 إظهار دائرة التحليل للوحدة:', unit.name);
                                  }}
                                  className="p-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                                  title="إظهار الدائرة"
                                >
                                  <Eye size={14} />
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    // تشغيل الأنيميشن لتدمير الهدف
                                    const [unitLat, unitLng] = getUnitCenter(unit);
                                    const [targetLat, targetLng] = getUnitCenter(targetUnit!);
                                    setAnimationConfig({
                                      source: [unitLat, unitLng],
                                      target: [targetLat, targetLng],
                                      isPlaying: true
                                    });
                                    console.log('💥 تدمير الهدف من', unit.name, 'إلى', targetUnit!.name);
                                  }}
                                  className="p-1 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                                  title="تدمير الهدف"
                                >
                                  <Zap size={14} />
                                </button>
                                <div className="text-right">
                                  <div className="text-sm font-medium">{unit.readiness}%</div>
                                  <div className="text-xs text-gray-500">{unit.type}</div>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex justify-end space-x-3 space-x-reverse">
                  <button
                    onClick={() => setShowTargetingUI(false)}
                    className="px-4 py-2 border border-gray-300 rounded-md"
                  >
                    إغلاق
                  </button>
                  {friendlyUnitsInRange.length > 0 && (
                    <>
                      <button
                        onClick={() => {
                          // حفظ القوات القادرة على الوصول وإظهار دوائر التحليل لها
                          console.log('🔵 إظهار دوائر التحليل للقوات:', friendlyUnitsInRange.map(unit => unit.name));
                          setMultipleSelectedUnits(friendlyUnitsInRange.map(unit => unit.id));
                          setShowCircleAnalysisMultiple(true);
                          setShowTargetingUI(false);
                          console.log('🔵 تم تفعيل عرض الدوائر المتعددة');
                        }}
                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                      >
                        إظهار جميع الدوائر
                      </button>
                      <button
                        onClick={() => {
                          // تشغيل أنيميشن متتالي لتدمير الهدف من جميع القوات
                          console.log('💥 تدمير الهدف من جميع القوات');
                          let delay = 0;
                          friendlyUnitsInRange.forEach((unit) => {
                            setTimeout(() => {
                              const [unitLat, unitLng] = getUnitCenter(unit);
                              const [targetLat, targetLng] = getUnitCenter(targetUnit!);
                              setAnimationConfig({
                                source: [unitLat, unitLng],
                                target: [targetLat, targetLng],
                                isPlaying: true
                              });
                            }, delay);
                            delay += 1500; // تأخير 1.5 ثانية بين كل هجوم
                          });
                        }}
                        className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center gap-2"
                      >
                        <Zap size={16} />
                        تدمير الهدف من الجميع
                      </button>
                    </>
                  )}
                </div>
              </div>
            );
          })()
        )}
        
        {/* Battle Scenario UI */}
        {showBattleScenarioUI && battleScenario && (
          <div className="absolute inset-x-0 bottom-0 z-[9999] rounded-t-lg bg-white p-4 shadow-lg max-h-[60vh] overflow-y-auto" style={{zIndex: 9999}}>
            <div className="mb-4 flex items-center justify-between">
              <h3 className="text-lg font-bold">سيناريو المعركة</h3>
              <button
                onClick={() => setShowBattleScenarioUI(false)}
                className="rounded-full p-1 hover:bg-gray-100"
              >
                <X size={20} />
              </button>
            </div>
            
            <div className="mb-4">
              <div className="font-medium mb-2">نوع العملية: {battleScenario.type === 'attack' ? 'هجوم' : 'دفاع'}</div>
              <div className="text-sm text-gray-500 mb-2">احتمالية النجاح: {battleScenario.probability}%</div>
              <div className="text-sm text-gray-500 mb-2">الخسائر المتوقعة: {battleScenario.estimatedLosses}%</div>
            </div>
            
            <div className="mb-4">
              <h4 className="font-medium mb-2">القوات المهاجمة</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {battleScenario.attackerUnitIds.map(unitId => {
                  const unit = getUnitById(unitId);
                  return unit ? (
                    <div key={unitId} className="p-2 bg-green-50 rounded border">
                      <div className="font-medium text-sm">{unit.name}</div>
                      <div className="text-xs text-gray-500">{unit.type}</div>
                    </div>
                  ) : null;
                })}
              </div>
            </div>
            
            <div className="mb-4">
              <h4 className="font-medium mb-2">الهدف</h4>
              <div className="p-3 bg-yellow-50 rounded border">
                <div className="font-medium">الموقع المستهدف</div>
                <div className="text-sm text-gray-500">
                  خط العرض: {battleScenario.targetPosition?.[0].toFixed(4)}
                </div>
                <div className="text-sm text-gray-500">
                  خط الطول: {battleScenario.targetPosition?.[1].toFixed(4)}
                </div>
              </div>
            </div>
            
            <div className="mb-4 rounded-lg border bg-blue-50 p-3">
              <h4 className="mb-2 font-medium text-blue-800">خطة العملية</h4>
              <ul className="text-sm space-y-1">
                <li className="flex">
                  <ArrowDown className="mt-1 h-4 w-4 flex-shrink-0" />
                  <span className="mr-2">
                    التقدم نحو الهدف عبر المسار المحدد
                  </span>
                </li>
                <li className="flex">
                  <ArrowDown className="mt-1 h-4 w-4 flex-shrink-0" />
                  <span className="mr-2">
                    إطلاق النار على الأهداف المعادية
                  </span>
                </li>
                <li className="flex">
                  <ArrowDown className="mt-1 h-4 w-4 flex-shrink-0" />
                  <span className="mr-2">
                    تأمين المنطقة المحررة
                  </span>
                </li>
                <li className="flex">
                  <ArrowDown className="mt-1 h-4 w-4 flex-shrink-0" />
                  <span className="mr-2">
                    تأمين طرق الإمداد والانسحاب أثناء العملية
                  </span>
                </li>
              </ul>
            </div>
            
            <div className="mb-4 rounded-lg border bg-yellow-50 p-3">
              <h4 className="mb-2 font-medium text-yellow-800">خيارات إضافية</h4>
              <div className="grid grid-cols-2 gap-2">
                <button className="rounded-md border border-gray-300 bg-white p-2 text-sm hover:bg-gray-50">
                  طلب دعم جوي
                </button>
                <button className="rounded-md border border-gray-300 bg-white p-2 text-sm hover:bg-gray-50">
                  طلب استطلاع متقدم
                </button>
                <button className="rounded-md border border-gray-300 bg-white p-2 text-sm hover:bg-gray-50">
                  تنسيق مع القوات المجاورة
                </button>
                <button className="rounded-md border border-gray-300 bg-white p-2 text-sm hover:bg-gray-50">
                  طلب تعزيزات
                </button>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 space-x-reverse">
              <button
                onClick={() => setShowBattleScenarioUI(false)}
                className="rounded-md border border-gray-300 px-4 py-2"
              >
                إلغاء
              </button>
              <button
                onClick={executeBattleScenario}
                className="flex items-center rounded-md bg-blue-600 px-4 py-2 text-white"
              >
                <ZapIcon size={18} className="ml-2" />
                <span>تنفيذ العملية</span>
              </button>
            </div>
          </div>
        )}
        
        {/* Map Gallery */}
        {showMapGallery && (
          <MapGallery 
            images={mapImages}
            onClose={() => setShowMapGallery(false)}
            onSelectImage={selectSatelliteImage}
          />
        )}
        
        {/* Spatial Analysis UI */}
        {showSpatialAnalysisUI && (
          <div className="absolute inset-x-0 bottom-0 z-[9999] rounded-t-lg bg-white p-4 shadow-lg max-h-[50vh]" style={{zIndex: 9999, overflowY: 'auto'}}>
            <div className="mb-4 flex items-center justify-between">
              <h3 className="text-lg font-bold">تحليل مكاني للقوات - دوائر التحليل</h3>
              <button
                onClick={() => setShowSpatialAnalysisUI(false)}
                className="rounded-full p-1 hover:bg-gray-100"
              >
                <X size={20} />
              </button>
            </div>
            <div className="mb-4 p-3 bg-blue-50 rounded-lg">
              <div className="text-sm text-blue-800 font-medium mb-2">📊 تحليل التهديدات حسب دوائر قوات العدو</div>
              <div className="text-xs text-blue-600 mb-3">يعرض التشكيلات الصديقة التي تقع ضمن دوائر التحليل لقوات العدو (عمى، قتل، عدم قتل)</div>
              <button
                onClick={() => {
                  setShowThreatenedUnitsFlash(true);
                  setTimeout(() => setShowThreatenedUnitsFlash(false), 3000);
                }}
                className="px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors text-sm"
              >
                🚨 إظهار فلاش أحمر للوحدات المهددة
              </button>
            </div>
            <div>
              {getThreatenedFriendlyUnits().length === 0 ? (
                <div className="text-gray-500 p-4 text-center bg-gray-50 rounded-lg">
                  ✅ لا توجد قوات صديقة ضمن دوائر تحليل قوات العدو
                </div>
              ) : (
                <ul className="flex flex-col gap-3">
                  {getThreatenedFriendlyUnits().map(unit => {
                    // تحديد لون الخلفية حسب مستوى الخطر
                    const bgColor = unit.maxSeverity === 'critical' ? 'bg-red-100 border-red-300' :
                                   unit.maxSeverity === 'high' ? 'bg-orange-100 border-orange-300' :
                                   'bg-yellow-100 border-yellow-300';

                    return (
                      <li key={unit.id} className={`rounded-md border-2 p-3 ${bgColor}`}>
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                          <div className="flex-1">
                            <div className="font-medium text-lg">{unit.name}</div>
                            <div className="text-sm text-gray-700 mt-1">
                              🎯 أقرب عدو: <span className="font-medium">{unit.nearestEnemy?.name}</span>
                              ({unit.minDist.toFixed(1)} كم)
                            </div>
                            <div className="text-sm text-gray-700">
                              ⚡ الجاهزية: <span className="font-medium">{unit.readiness}%</span>
                            </div>
                            <div className="text-sm font-medium mt-2">
                              🔴 نوع التهديد: <span className="text-red-600">{unit.rangeType}</span>
                            </div>
                            <div className="text-xs text-gray-600 mt-1">
                              مهددة من {unit.threatenedBy.length} وحدة عدو
                            </div>
                          </div>
                          <div className="flex flex-col md:flex-row md:items-center md:space-x-2 md:space-x-reverse mt-2 md:mt-0">
                        <button
                          className="rounded-md bg-blue-600 px-3 py-1 text-white hover:bg-blue-700 mb-1 md:mb-0"
                          onClick={() => {
                            setMoveMode(true);
                            setMoveSourceUnitId(unit.id);
                            setShowSpatialAnalysisUI(false);
                          }}
                        >
                          اقتراح تحريك
                        </button>
                        <button
                          className="rounded-md bg-green-600 px-3 py-1 text-white hover:bg-green-700"
                          onClick={() => alert('اقتراح تعزيز لهذه الوحدة')}
                        >
                          اقتراح تعزيز
                        </button>
                          </div>
                        </div>
                      </li>
                    );
                  })}
                </ul>
              )}
            </div>
          </div>
        )}
        
        {/* فلاش اختيار الوحدة */}
        {showCircleAnalysisFlash && (
          <div className="absolute inset-0 flex items-center justify-center z-[99999] pointer-events-none">
            <div className="bg-white border-2 border-green-400 shadow-lg rounded-lg px-8 py-6 text-xl font-bold text-green-700 animate-pulse">
              اختر الوحدة القتالية
            </div>
          </div>
        )}
        

        {/* واجهة تهيئة أبعاد الدوائر */}
        {showCircleConfig && (
          <CircleRangeConfig
            onClose={() => setShowCircleConfig(false)}
            onSave={cfg => { setCircleConfig(cfg); setShowCircleConfig(false); }}
          />
        )}
        
{/* Los componentes de análisis espacial avanzado se han movido dentro del MapContainer */}
        
        {/* Componentes de simulación de combate y planificación de escenarios */}
        {showBattleSimulator && (
          <BattleSimulator 
            onClose={handleCloseBattleSimulator}
            onSimulationComplete={handleSimulationComplete}
          />
        )}
        
        {/* واجهة اختيار القوات للمعركة */}
        {showBattleForceSelector && (
          <BattleForceSelector
            friendlyUnits={unitsData.filter(u => u.side === 'friendly')}
            enemyUnits={unitsData.filter(u => u.side === 'enemy')}
            equipment={equipmentData}
            onConfirm={handleBattleForceSelection}
            onCancel={handleCloseBattleForceSelector}
          />
        )}
        
        {/* تأثيرات محاكاة المعركة التلقائية */}
        {showBattleSimulationEffects && (
          <BattleSimulationEffects
            simulationResult={battleSimulationResult}
            isActive={autoSimulationActive}
            selectedForces={selectedBattleForces}
            attackerCoordinates={unitsData.filter(u => selectedBattleForces?.friendlyUnits.includes(u.id) || u.side === 'friendly').map(u => {
              const coords = u.location.type === 'point' 
                ? u.location.coordinates[0] as [number, number]
                : [(u.location.coordinates as number[][][])[0][0][0], (u.location.coordinates as number[][][])[0][0][1]];
              return [coords[0], coords[1]] as [number, number];
            })}
            defenderCoordinates={unitsData.filter(u => selectedBattleForces?.enemyUnits.includes(u.id) || u.side === 'enemy').map(u => {
              const coords = u.location.type === 'point' 
                ? u.location.coordinates[0] as [number, number]
                : [(u.location.coordinates as number[][][])[0][0][0], (u.location.coordinates as number[][][])[0][0][1]];
              return [coords[0], coords[1]] as [number, number];
            })}
            onEffectsComplete={handleBattleEffectsComplete}
          />
        )}
        
        {showScenarioPlanner && (
          <ScenarioPlanner 
            onClose={handleCloseScenarioPlanner}
            onScenarioSelected={handleScenarioSelected}
          />
        )}
        
        {/* Real Battle Simulation - moved inside MapContainer */}
        {showRealBattleSimulation && (
          <RealBattleSimulation onClose={() => setShowRealBattleSimulation(false)} />
        )}
      </div>
      
      {/* Battle Controls Overlay */}
      {showRealBattleSimulation && (
        <div className="absolute top-4 left-4 z-[2000] bg-white rounded-lg shadow-lg p-4">
          <div className="flex flex-col gap-2">
            <button 
              onClick={() => setShowRealBattleSimulation(false)}
              className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
            >
              ❌ إغلاق المحاكاة
            </button>
            <div className="text-sm text-gray-600">
              ⚡ محاكاة معركة حقيقية نشطة
            </div>
          </div>
        </div>
      )}

      {/* Advanced Battle Simulation */}
      {showAdvancedBattleSimulation && (
        <AdvancedBattleSimulation
          onClose={() => setShowAdvancedBattleSimulation(false)}
          selectedFriendlyUnits={selectedFriendlyUnits}
          selectedEnemyUnits={selectedEnemyUnits}
          targetingData={currentTargetingData || undefined}
        />
      )}

      {/* Map Battle Simulation */}
      {mapBattleActive && (
        <MapBattleSimulation
          selectedFriendlyUnits={selectedFriendlyUnits}
          selectedEnemyUnits={selectedEnemyUnits}
          onBattleComplete={handleBattleComplete}
          targetingData={currentTargetingData || undefined}
        />
      )}

      {/* Battle Result Flash */}
      {battleResult && (
        <BattleResultFlash
          result={battleResult}
          onClose={() => setBattleResult(null)}
        />
      )}
    </div>
  );
}
 